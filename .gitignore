# Miscellaneous
*.class
*.log
*.pyc
*.swp
.DS_Store
.atom/
.buildlog/
.history
.svn/
nodemon.json
.fvm

# IntelliJ related
*.iml
*.ipr
*.iws
.idea/
.vscode/

# The .vscode folder contains launch configuration and tasks you configure in
# VS Code which you may wish to be included in version control, so this line
# is commented out by default.
#.vscode/

# Flutter/Dart/Pub related
**/doc/api/
**/ios/Flutter/.last_build_id
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.packages
.pub-cache/
.pub/
/build/
# /changesetup

# Web related

# Symbolication related
app.*.symbols

# Obfuscation related
app.*.map.json

# FVM Version Cache
.fvm/


# Platform
/macos/
/linux/
/.qodo/
ios/build/ios/Pods.build/Release-iphonesimulator/DKImagePickerController-DKImagePickerController.build/dgph
ios/build/ios/Pods.build/Release-iphonesimulator/DKImagePickerController.build/dgph
ios/build/ios/Pods.build/Release-iphonesimulator/DKPhotoGallery-DKPhotoGallery.build/dgph
ios/build/ios/Pods.build/Release-iphonesimulator/DKPhotoGallery.build/dgph
ios/build/ios/Pods.build/Release-iphonesimulator/FMDB.build/dgph
ios/build/ios/Pods.build/Release-iphonesimulator/Firebase.build/dgph
ios/build/ios/Pods.build/Release-iphonesimulator/FirebaseAnalytics.build/dgph
ios/build/ios/Pods.build/Release-iphonesimulator/FirebaseCore.build/dgph
ios/build/ios/Pods.build/Release-iphonesimulator/FirebaseCoreInternal.build/dgph
ios/build/ios/Pods.build/Release-iphonesimulator/FirebaseDynamicLinks.build/dgph
ios/build/ios/Pods.build/Release-iphonesimulator/FirebaseInstallations.build/dgph
ios/build/ios/Pods.build/Release-iphonesimulator/FirebaseMessaging.build/dgph
ios/build/ios/Pods.build/Release-iphonesimulator/Flutter.build/dgph
ios/build/ios/Pods.build/Release-iphonesimulator/GoogleAppMeasurement.build/dgph
ios/build/ios/Pods.build/Release-iphonesimulator/GoogleDataTransport.build/dgph
ios/build/ios/Pods.build/Release-iphonesimulator/GoogleUtilities.build/dgph
ios/build/ios/Pods.build/Release-iphonesimulator/MTBBarcodeScanner.build/dgph
ios/build/ios/Pods.build/Release-iphonesimulator/OrderedSet.build/dgph
ios/build/ios/Pods.build/Release-iphonesimulator/Pods-Runner.build/dgph
ios/build/ios/Pods.build/Release-iphonesimulator/PromisesObjC.build/dgph
ios/build/ios/Pods.build/Release-iphonesimulator/SDWebImage.build/dgph
ios/build/ios/Pods.build/Release-iphonesimulator/SwiftyGif.build/dgph
ios/build/ios/Pods.build/Release-iphonesimulator/TOCropViewController-TOCropViewControllerBundle.build/dgph
ios/build/ios/Pods.build/Release-iphonesimulator/TOCropViewController.build/dgph
ios/build/ios/Pods.build/Release-iphonesimulator/Try.build/dgph
ios/build/ios/Pods.build/Release-iphonesimulator/app_tracking_transparency.build/dgph
ios/build/ios/Pods.build/Release-iphonesimulator/audio_waveforms.build/dgph
ios/build/ios/Pods.build/Release-iphonesimulator/audioplayers_darwin.build/dgph
ios/build/ios/Pods.build/Release-iphonesimulator/camera_avfoundation.build/dgph
ios/build/ios/Pods.build/Release-iphonesimulator/device_info_plus.build/dgph
ios/build/ios/Pods.build/Release-iphonesimulator/devicelocale.build/dgph
ios/build/ios/Pods.build/Release-iphonesimulator/ffmpeg-kit-ios-https.build/dgph
ios/build/ios/Pods.build/Release-iphonesimulator/ffmpeg_kit_flutter.build/dgph
ios/build/ios/Pods.build/Release-iphonesimulator/file_picker.build/dgph
ios/build/ios/Pods.build/Release-iphonesimulator/firebase_analytics.build/dgph
ios/build/ios/Pods.build/Release-iphonesimulator/firebase_core.build/dgph
ios/build/ios/Pods.build/Release-iphonesimulator/firebase_dynamic_links.build/dgph
ios/build/ios/Pods.build/Release-iphonesimulator/firebase_messaging.build/dgph
ios/build/ios/Pods.build/Release-iphonesimulator/flutter_downloader-FlutterDownloaderDatabase.build/dgph
ios/build/ios/Pods.build/Release-iphonesimulator/flutter_downloader.build/dgph
ios/build/ios/Pods.build/Release-iphonesimulator/flutter_inappwebview.build/dgph
ios/build/ios/Pods.build/Release-iphonesimulator/flutter_local_notifications.build/dgph
ios/build/ios/Pods.build/Release-iphonesimulator/flutter_pdfview.build/dgph
ios/build/ios/Pods.build/Release-iphonesimulator/flutter_timezone.build/dgph
ios/build/ios/Pods.build/Release-iphonesimulator/geocoding_ios.build/dgph
ios/build/ios/Pods.build/Release-iphonesimulator/image_cropper.build/dgph
ios/build/ios/Pods.build/Release-iphonesimulator/image_picker_ios.build/dgph
ios/build/ios/Pods.build/Release-iphonesimulator/libwebp.build/dgph
ios/build/ios/Pods.build/Release-iphonesimulator/location.build/dgph
ios/build/ios/Pods.build/Release-iphonesimulator/nanopb.build/dgph
ios/build/ios/Pods.build/Release-iphonesimulator/open_filex.build/dgph
ios/build/ios/Pods.build/Release-iphonesimulator/otp_autofill.build/dgph
ios/build/ios/Pods.build/Release-iphonesimulator/package_info_plus.build/dgph
ios/build/ios/Pods.build/Release-iphonesimulator/path_provider_foundation.build/dgph
ios/build/ios/Pods.build/Release-iphonesimulator/permission_handler_apple.build/dgph
ios/build/ios/Pods.build/Release-iphonesimulator/qr_code_scanner.build/dgph
ios/build/ios/Pods.build/Release-iphonesimulator/record.build/dgph
ios/build/ios/Pods.build/Release-iphonesimulator/share_plus.build/dgph
ios/build/ios/Pods.build/Release-iphonesimulator/shared_preferences_ios.build/dgph
ios/build/ios/Pods.build/Release-iphonesimulator/smart_auth.build/dgph
ios/build/ios/Pods.build/Release-iphonesimulator/speech_to_text.build/dgph
ios/build/ios/Pods.build/Release-iphonesimulator/sqflite.build/dgph
ios/build/ios/Pods.build/Release-iphonesimulator/url_launcher_ios.build/dgph
ios/build/ios/Pods.build/Release-iphonesimulator/video_player_avfoundation.build/dgph
ios/build/ios/Pods.build/Release-iphonesimulator/video_thumbnail.build/dgph
ios/build/ios/Pods.build/Release-iphonesimulator/wakelock.build/dgph
ios/build/ios/Pods.build/Release-iphonesimulator/webview_flutter_wkwebview.build/dgph
