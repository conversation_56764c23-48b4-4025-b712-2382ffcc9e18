PODS:
  - app_tracking_transparency (0.0.1):
    - Flutter
  - audio_waveforms (0.0.1):
    - Flutter
  - devicelocale (0.0.1):
    - Flutter
  - Flutter (1.0.0)
  - flutter_downloader (0.0.1):
    - Flutter
  - flutter_inappwebview_ios (0.0.1):
    - Flutter
    - flutter_inappwebview_ios/Core (= 0.0.1)
    - OrderedSet (~> 6.0.3)
  - flutter_inappwebview_ios/Core (0.0.1):
    - Flutter
    - OrderedSet (~> 6.0.3)
  - flutter_isolate (0.0.1):
    - Flutter
  - flutter_keyboard_visibility (0.0.1):
    - Flutter
  - flutter_native_video_trimmer (1.0.0):
    - Flutter
  - get_thumbnail_video (0.0.1):
    - Flutter
    - libwebp
  - image_picker_ios (0.0.1):
    - Flutter
  - install_referrer (1.0.0):
    - Flutter
  - libwebp (1.5.0):
    - libwebp/demux (= 1.5.0)
    - libwebp/mux (= 1.5.0)
    - libwebp/sharpyuv (= 1.5.0)
    - libwebp/webp (= 1.5.0)
  - libwebp/demux (1.5.0):
    - libwebp/webp
  - libwebp/mux (1.5.0):
    - libwebp/demux
  - libwebp/sharpyuv (1.5.0)
  - libwebp/webp (1.5.0):
    - libwebp/sharpyuv
  - location (0.0.1):
    - Flutter
  - open_filex (0.0.2):
    - Flutter
  - OrderedSet (6.0.3)
  - otp_autofill (0.0.1):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.3.0):
    - Flutter
  - simple_barcode_scanner (0.1.7):
    - Flutter
  - sqflite (0.0.3):
    - Flutter
    - FlutterMacOS
  - url_launcher_ios (0.0.1):
    - Flutter
  - video_compress (0.3.0):
    - Flutter
  - video_thumbnail (0.0.1):
    - Flutter
    - libwebp

DEPENDENCIES:
  - app_tracking_transparency (from `.symlinks/plugins/app_tracking_transparency/ios`)
  - audio_waveforms (from `.symlinks/plugins/audio_waveforms/ios`)
  - devicelocale (from `.symlinks/plugins/devicelocale/ios`)
  - Flutter (from `Flutter`)
  - flutter_downloader (from `.symlinks/plugins/flutter_downloader/ios`)
  - flutter_inappwebview_ios (from `.symlinks/plugins/flutter_inappwebview_ios/ios`)
  - flutter_isolate (from `.symlinks/plugins/flutter_isolate/ios`)
  - flutter_keyboard_visibility (from `.symlinks/plugins/flutter_keyboard_visibility/ios`)
  - flutter_native_video_trimmer (from `.symlinks/plugins/flutter_native_video_trimmer/ios`)
  - get_thumbnail_video (from `.symlinks/plugins/get_thumbnail_video/ios`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - install_referrer (from `.symlinks/plugins/install_referrer/ios`)
  - location (from `.symlinks/plugins/location/ios`)
  - open_filex (from `.symlinks/plugins/open_filex/ios`)
  - otp_autofill (from `.symlinks/plugins/otp_autofill/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - simple_barcode_scanner (from `.symlinks/plugins/simple_barcode_scanner/ios`)
  - sqflite (from `.symlinks/plugins/sqflite/darwin`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - video_compress (from `.symlinks/plugins/video_compress/ios`)
  - video_thumbnail (from `.symlinks/plugins/video_thumbnail/ios`)

SPEC REPOS:
  trunk:
    - libwebp
    - OrderedSet

EXTERNAL SOURCES:
  app_tracking_transparency:
    :path: ".symlinks/plugins/app_tracking_transparency/ios"
  audio_waveforms:
    :path: ".symlinks/plugins/audio_waveforms/ios"
  devicelocale:
    :path: ".symlinks/plugins/devicelocale/ios"
  Flutter:
    :path: Flutter
  flutter_downloader:
    :path: ".symlinks/plugins/flutter_downloader/ios"
  flutter_inappwebview_ios:
    :path: ".symlinks/plugins/flutter_inappwebview_ios/ios"
  flutter_isolate:
    :path: ".symlinks/plugins/flutter_isolate/ios"
  flutter_keyboard_visibility:
    :path: ".symlinks/plugins/flutter_keyboard_visibility/ios"
  flutter_native_video_trimmer:
    :path: ".symlinks/plugins/flutter_native_video_trimmer/ios"
  get_thumbnail_video:
    :path: ".symlinks/plugins/get_thumbnail_video/ios"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  install_referrer:
    :path: ".symlinks/plugins/install_referrer/ios"
  location:
    :path: ".symlinks/plugins/location/ios"
  open_filex:
    :path: ".symlinks/plugins/open_filex/ios"
  otp_autofill:
    :path: ".symlinks/plugins/otp_autofill/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  simple_barcode_scanner:
    :path: ".symlinks/plugins/simple_barcode_scanner/ios"
  sqflite:
    :path: ".symlinks/plugins/sqflite/darwin"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  video_compress:
    :path: ".symlinks/plugins/video_compress/ios"
  video_thumbnail:
    :path: ".symlinks/plugins/video_thumbnail/ios"

SPEC CHECKSUMS:
  app_tracking_transparency: e169b653478da7bb15a6c61209015378ca73e375
  audio_waveforms: cd736909ebc6b6a164eb74701d8c705ce3241e1c
  devicelocale: 35ba84dc7f45f527c3001535d8c8d104edd5d926
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_downloader: b7301ae057deadd4b1650dc7c05375f10ff12c39
  flutter_inappwebview_ios: 6f63631e2c62a7c350263b13fa5427aedefe81d4
  flutter_isolate: 0edf5081826d071adf21759d1eb10ff5c24503b5
  flutter_keyboard_visibility: 0339d06371254c3eb25eeb90ba8d17dca8f9c069
  flutter_native_video_trimmer: 3a81cd49c8b789ed448a5d1606617633550496b5
  get_thumbnail_video: b9a180957daed3e9179e66268db51d8798e41f65
  image_picker_ios: 99dfe1854b4fa34d0364e74a78448a0151025425
  install_referrer: 1bde9acf8b1bfd372706454234444a01b5732bcf
  libwebp: 02b23773aedb6ff1fd38cec7a77b81414c6842a8
  location: d5cf8598915965547c3f36761ae9cc4f4e87d22e
  open_filex: 6e26e659846ec990262224a12ef1c528bb4edbe4
  OrderedSet: e539b66b644ff081c73a262d24ad552a69be3a94
  otp_autofill: 93bcc21475705d35445927cb9aef24e818bbf083
  path_provider_foundation: 3784922295ac71e43754bd15e0653ccfd36a147c
  permission_handler_apple: 9878588469a2b0d0fc1e048d9f43605f92e6cec2
  simple_barcode_scanner: bb2603ae5228a007bd066e9f955911858ae55755
  sqflite: 673a0e54cc04b7d6dba8d24fb8095b31c3a99eec
  url_launcher_ios: bbd758c6e7f9fd7b5b1d4cde34d2b95fcce5e812
  video_compress: fce97e4fb1dfd88175aa07d2ffc8a2f297f87fbe
  video_thumbnail: c4e2a3c539e247d4de13cd545344fd2d26ffafd1

PODFILE CHECKSUM: 369b6b06ac4ab15280b97ae3fe99bb9f9a1bf7de

COCOAPODS: 1.16.2
